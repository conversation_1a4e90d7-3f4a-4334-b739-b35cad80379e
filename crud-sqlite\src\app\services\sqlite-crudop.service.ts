import { Injectable } from '@angular/core';
import { Capacitor } from '@capacitor/core';
import {
  CapacitorSQLite,
  SQLiteConnection,
  SQLiteDBConnection
} from '@capacitor-community/sqlite';

@Injectable({ providedIn: 'root' })
export class SqliteCrudopService {
  // ✅ Create a SQLiteConnection instance
  private sqlite = new SQLiteConnection(CapacitorSQLite);
  private db: SQLiteDBConnection | null = null;
  private isInitialized = false;

  // ✅ Initialize DB
  async initDB(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('Initializing SQLite database...');

      // ✅ For web, initialize IndexedDB store
      if (Capacitor.getPlatform() === 'web') {
        await this.sqlite.initWebStore();
      }

      // ✅ Correct API to create connection
     // ✅ No explicit type required
      const conn = await this.sqlite.createConnection(
        'mydb',
        false,
        'no-encryption',
        1
      );

      // ✅ Open DB connection
      await conn.open();
      this.db = conn;

      // ✅ Create table
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT NOT NULL
        );
      `);

      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing database:', error);
      throw error;
    }
  }

  // ✅ Ensure DB is ready
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized || !this.db) {
      await this.initDB();
    }
  }

  // ✅ Fetch all users
  async getAllUsers(): Promise<any[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    try {
      const result = await this.db.query('SELECT * FROM users');
      return result.values || [];
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      return [];
    }
  }

  // ✅ Add user
  async addUser(name: string, email: string): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run(
        'INSERT INTO users (name, email) VALUES (?, ?)',
        [name, email]
      );
    } catch (error) {
      console.error('❌ Error adding user:', error);
      throw error;
    }
  }

  // ✅ Update user
  async updateUser(id: number, name: string, email: string): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run(
        'UPDATE users SET name = ?, email = ? WHERE id = ?',
        [name, email, id]
      );
    } catch (error) {
      console.error('❌ Error updating user:', error);
      throw error;
    }
  }

  // ✅ Delete user
  async deleteUser(id: number): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run('DELETE FROM users WHERE id = ?', [id]);
    } catch (error) {
      console.error('❌ Error deleting user:', error);
      throw error;
    }
  }
}

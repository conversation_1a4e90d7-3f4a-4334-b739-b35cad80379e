import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// ✅ Import standalone Ionic components directly
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonItem,
  IonInput,
  IonButton,
  IonList,
  IonLabel
} from '@ionic/angular/standalone';

import { SqliteCrudopService } from 'src/app/services/sqlite-crudop.service';

@Component({
  selector: 'app-sqlite-crudop',
  standalone: true,
  templateUrl: './sqlite-crudop.page.html',
  styleUrls: ['./sqlite-crudop.page.scss'],
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonItem,
    IonInput,
    IonButton,
    IonList,
    IonLabel
  ]
})
export class SqliteCrudopPage implements OnInit {
  users: any[] = [];
  name = '';
  email = '';

  constructor(private sqlite: SqliteCrudopService) {}

  async ngOnInit() {
    try {
      await this.sqlite.initDB();
      await this.loadUsers();
    } catch (error) {
      console.error('Error during initialization:', error);
    }
  }

  async loadUsers() {
    try {
      this.users = await this.sqlite.getAllUsers();
    } catch (error) {
      console.error('Error loading users:', error);
    }
  }

  async addUser() {
    if (!this.name.trim() || !this.email.trim()) {
      console.error('Name and email are required');
      return;
    }

    try {
      await this.sqlite.addUser(this.name, this.email);
      this.name = '';
      this.email = '';
      await this.loadUsers();
    } catch (error) {
      console.error('Error adding user:', error);
    }
  }

  async deleteUser(id: number) {
    try {
      await this.sqlite.deleteUser(id);
      await this.loadUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  }
}

import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IonApp, IonRouterOutlet } from '@ionic/angular/standalone';
import { Platform } from '@ionic/angular';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  imports: [IonApp, IonRouterOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppComponent implements OnInit {
  constructor(private platform: Platform) {}

  async ngOnInit() {
    await this.platform.ready();
    await this.initializeJeepSqlite();
  }

  private async initializeJeepSqlite() {
    try {
      // Check if we're on web platform
      const isWeb = (window as any).Capacitor?.getPlatform() === 'web';

      if (isWeb) {
        // Create and append jeep-sqlite element if it doesn't exist
        if (!document.querySelector('jeep-sqlite')) {
          const jeepSqlite = document.createElement('jeep-sqlite');
          document.body.appendChild(jeepSqlite);
          console.log('jeep-sqlite element added to DOM');
        }

        // Wait for the element to be defined
        await customElements.whenDefined('jeep-sqlite');

        // Get the element and initialize it
        const jeepSqlite = document.querySelector('jeep-sqlite');
        if (jeepSqlite) {
          // Wait for component to be ready
          if (typeof (jeepSqlite as any).componentOnReady === 'function') {
            await (jeepSqlite as any).componentOnReady();
          }

          // Initialize web store if available
          if (typeof (jeepSqlite as any).initWebStore === 'function') {
            await (jeepSqlite as any).initWebStore();
            console.log('jeep-sqlite web store initialized');
          }
        }
      }
    } catch (error) {
      console.error('Error initializing jeep-sqlite:', error);
    }
  }
}

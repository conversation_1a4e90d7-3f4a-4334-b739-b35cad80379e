// Debug script to check SQLite functionality
// Paste this into browser console to debug

console.log('=== SQLite Debug Script ===');

// Check if Capacitor is available
console.log('1. Capacitor available:', typeof window.Capacitor !== 'undefined');
console.log('   Platform:', window.Capacitor?.getPlatform());

// Check if jeep-sqlite is loaded
console.log('2. jeep-sqlite element in DOM:', !!document.querySelector('jeep-sqlite'));
console.log('   jeep-sqlite custom element defined:', !!window.customElements.get('jeep-sqlite'));

// Check if CapacitorSQLite is available
console.log('3. CapacitorSQLite available:', typeof window.CapacitorSQLite !== 'undefined');

// Check Angular app
console.log('4. Angular app element:', !!document.querySelector('app-root'));

// Check for any console errors
console.log('5. Check console for any errors above this message');

// Test jeep-sqlite initialization
if (document.querySelector('jeep-sqlite')) {
    const jeepSqlite = document.querySelector('jeep-sqlite');
    console.log('6. jeep-sqlite element:', jeepSqlite);
    console.log('   componentOnReady method:', typeof jeepSqlite.componentOnReady);
    console.log('   initWebStore method:', typeof jeepSqlite.initWebStore);
} else {
    console.log('6. jeep-sqlite element not found');
}

// Check if SQLite service is working
setTimeout(() => {
    console.log('7. Checking after 2 seconds...');
    console.log('   jeep-sqlite element now:', !!document.querySelector('jeep-sqlite'));
}, 2000);

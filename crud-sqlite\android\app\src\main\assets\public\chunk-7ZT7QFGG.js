import{a as s}from"./chunk-ZXCQVRLE.js";import{H as a,e as o,g as i,h as r,i as c,j as p}from"./chunk-JE2PG4HF.js";import"./chunk-QUM6RZVN.js";import"./chunk-6WAW2KHA.js";import"./chunk-YJ7VB3TT.js";import"./chunk-QDU6VP7L.js";import"./chunk-ICSGBKZQ.js";import"./chunk-YSN7XVTD.js";import"./chunk-R5HL6L5F.js";import"./chunk-4WFVMWDK.js";import"./chunk-M2X7KQLB.js";import"./chunk-2YSZFPCQ.js";import"./chunk-57YRIO75.js";import"./chunk-REYR55MP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import"./chunk-JHI3MBHO.js";var h=(()=>{let t=class t{constructor(){}};t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=o({type:t,selectors:[["app-home"]],decls:2,vars:1,consts:[[3,"fullscreen"]],template:function(e,m){e&1&&(r(0,"ion-content",0),p(1,"app-sqlite-crudop"),c()),e&2&&i("fullscreen",!0)},dependencies:[a,s],styles:["#container[_ngcontent-%COMP%]{text-align:center;position:absolute;left:0;right:0;top:50%;transform:translateY(-50%)}#container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:20px;line-height:26px}#container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:16px;line-height:22px;color:#8c8c8c;margin:0}#container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]});let n=t;return n})();export{h as HomePage};

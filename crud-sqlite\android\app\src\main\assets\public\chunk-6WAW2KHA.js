import{e as a}from"./chunk-JHI3MBHO.js";var E=function(r){return r.Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE",r}(E||{}),P=class extends Error{constructor(e,t,i){super(e),this.message=e,this.code=t,this.data=i}},M=r=>{var e,t;return r?.androidBridge?"android":!((t=(e=r?.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||t===void 0)&&t.bridge?"ios":"web"},K=r=>{let e=r.CapacitorCustomPlatform||null,t=r.Capacitor||{},i=t.Plugins=t.Plugins||{},s=()=>e!==null?e.name:M(r),n=()=>s()!=="web",c=o=>{let d=y.get(o);return!!(d?.platforms.has(s())||l(o))},l=o=>{var d;return(d=t.PluginHeaders)===null||d===void 0?void 0:d.find(k=>k.name===o)},f=o=>r.console.error(o),y=new Map,C=(o,d={})=>{let k=y.get(o);if(k)return console.warn(`Capacitor plugin "${o}" already registered. Cannot register plugins twice.`),k.proxy;let p=s(),L=l(o),b,q=()=>a(null,null,function*(){return!b&&p in d?b=typeof d[p]=="function"?b=yield d[p]():b=d[p]:e!==null&&!b&&"web"in d&&(b=typeof d.web=="function"?b=yield d.web():b=d.web),b}),B=(u,h)=>{var w,m;if(L){let v=L?.methods.find(g=>h===g.name);if(v)return v.rtype==="promise"?g=>t.nativePromise(o,h.toString(),g):(g,O)=>t.nativeCallback(o,h.toString(),g,O);if(u)return(w=u[h])===null||w===void 0?void 0:w.bind(u)}else{if(u)return(m=u[h])===null||m===void 0?void 0:m.bind(u);throw new P(`"${o}" plugin is not implemented on ${p}`,E.Unimplemented)}},x=u=>{let h,w=(...m)=>{let v=q().then(g=>{let O=B(g,u);if(O){let $=O(...m);return h=$?.remove,$}else throw new P(`"${o}.${u}()" is not implemented on ${p}`,E.Unimplemented)});return u==="addListener"&&(v.remove=()=>a(null,null,function*(){return h()})),v};return w.toString=()=>`${u.toString()}() { [capacitor code] }`,Object.defineProperty(w,"name",{value:u,writable:!1,configurable:!1}),w},R=x("addListener"),H=x("removeListener"),I=(u,h)=>{let w=R({eventName:u},h),m=()=>a(null,null,function*(){let g=yield w;H({eventName:u,callbackId:g},h)}),v=new Promise(g=>w.then(()=>g({remove:m})));return v.remove=()=>a(null,null,function*(){console.warn("Using addListener() without 'await' is deprecated."),yield m()}),v},U=new Proxy({},{get(u,h){switch(h){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return L?I:R;case"removeListener":return H;default:return x(h)}}});return i[o]=U,y.set(o,{name:o,proxy:U,platforms:new Set([...Object.keys(d),...L?[p]:[]])}),U};return t.convertFileSrc||(t.convertFileSrc=o=>o),t.getPlatform=s,t.handleError=f,t.isNativePlatform=n,t.isPluginAvailable=c,t.registerPlugin=C,t.Exception=P,t.DEBUG=!!t.DEBUG,t.isLoggingEnabled=!!t.isLoggingEnabled,t},W=r=>r.Capacitor=K(r),j=W(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),_=j.registerPlugin,A=class{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let i=!1;this.listeners[e]||(this.listeners[e]=[],i=!0),this.listeners[e].push(t);let n=this.windowListeners[e];n&&!n.registered&&this.addWindowListener(n),i&&this.sendRetainedArgumentsForEvent(e);let c=()=>a(this,null,function*(){return this.removeListener(e,t)});return Promise.resolve({remove:c})}removeAllListeners(){return a(this,null,function*(){this.listeners={};for(let e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}})}notifyListeners(e,t,i){let s=this.listeners[e];if(!s){if(i){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}return}s.forEach(n=>n(t))}hasListeners(e){var t;return!!(!((t=this.listeners[e])===null||t===void 0)&&t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:i=>{this.notifyListeners(t,i)}}}unimplemented(e="not implemented"){return new j.Exception(e,E.Unimplemented)}unavailable(e="not available"){return new j.Exception(e,E.Unavailable)}removeListener(e,t){return a(this,null,function*(){let i=this.listeners[e];if(!i)return;let s=i.indexOf(t);this.listeners[e].splice(s,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])})}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){let t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(i=>{this.notifyListeners(e,i)}))}};var D=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),F=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent),S=class extends A{getCookies(){return a(this,null,function*(){let e=document.cookie,t={};return e.split(";").forEach(i=>{if(i.length<=0)return;let[s,n]=i.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");s=F(s).trim(),n=F(n).trim(),t[s]=n}),t})}setCookie(e){return a(this,null,function*(){try{let t=D(e.key),i=D(e.value),s=`; expires=${(e.expires||"").replace("expires=","")}`,n=(e.path||"/").replace("path=",""),c=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${i||""}${s}; path=${n}; ${c};`}catch(t){return Promise.reject(t)}})}deleteCookie(e){return a(this,null,function*(){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}})}clearCookies(){return a(this,null,function*(){try{let e=document.cookie.split(";")||[];for(let t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}})}clearAllCookies(){return a(this,null,function*(){try{yield this.clearCookies()}catch(e){return Promise.reject(e)}})}},Q=_("CapacitorCookies",{web:()=>new S}),G=r=>a(null,null,function*(){return new Promise((e,t)=>{let i=new FileReader;i.onload=()=>{let s=i.result;e(s.indexOf(",")>=0?s.split(",")[1]:s)},i.onerror=s=>t(s),i.readAsDataURL(r)})}),V=(r={})=>{let e=Object.keys(r);return Object.keys(r).map(s=>s.toLocaleLowerCase()).reduce((s,n,c)=>(s[n]=r[e[c]],s),{})},z=(r,e=!0)=>r?Object.entries(r).reduce((i,s)=>{let[n,c]=s,l,f;return Array.isArray(c)?(f="",c.forEach(y=>{l=e?encodeURIComponent(y):y,f+=`${n}=${l}&`}),f.slice(0,-1)):(l=e?encodeURIComponent(c):c,f=`${n}=${l}`),`${i}&${f}`},"").substr(1):null,J=(r,e={})=>{let t=Object.assign({method:r.method||"GET",headers:r.headers},e),s=V(r.headers)["content-type"]||"";if(typeof r.data=="string")t.body=r.data;else if(s.includes("application/x-www-form-urlencoded")){let n=new URLSearchParams;for(let[c,l]of Object.entries(r.data||{}))n.set(c,l);t.body=n.toString()}else if(s.includes("multipart/form-data")||r.data instanceof FormData){let n=new FormData;if(r.data instanceof FormData)r.data.forEach((l,f)=>{n.append(f,l)});else for(let l of Object.keys(r.data))n.append(l,r.data[l]);t.body=n;let c=new Headers(t.headers);c.delete("content-type"),t.headers=c}else(s.includes("application/json")||typeof r.data=="object")&&(t.body=JSON.stringify(r.data));return t},T=class extends A{request(e){return a(this,null,function*(){let t=J(e,e.webFetchExtra),i=z(e.params,e.shouldEncodeUrlParams),s=i?`${e.url}?${i}`:e.url,n=yield fetch(s,t),c=n.headers.get("content-type")||"",{responseType:l="text"}=n.ok?e:{};c.includes("application/json")&&(l="json");let f,y;switch(l){case"arraybuffer":case"blob":y=yield n.blob(),f=yield G(y);break;case"json":f=yield n.json();break;case"document":case"text":default:f=yield n.text()}let C={};return n.headers.forEach((o,d)=>{C[d]=o}),{data:f,headers:C,status:n.status,url:n.url}})}get(e){return a(this,null,function*(){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))})}post(e){return a(this,null,function*(){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))})}put(e){return a(this,null,function*(){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))})}patch(e){return a(this,null,function*(){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))})}delete(e){return a(this,null,function*(){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))})}},X=_("CapacitorHttp",{web:()=>new T});export{_ as a,A as b};

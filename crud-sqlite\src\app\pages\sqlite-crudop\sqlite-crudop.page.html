<ion-header>
  <ion-toolbar>
    <ion-title>SQLite CRUD (Standalone)</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <ion-item>
    <ion-input placeholder="Name" [(ngModel)]="name"></ion-input>
  </ion-item>
  <ion-item>
    <ion-input placeholder="Email" [(ngModel)]="email"></ion-input>
  </ion-item>

  <ion-button expand="full" (click)="addUser()">Add User</ion-button>

  <ion-list>
    <ion-item *ngFor="let user of users">
      <ion-label>
        <h2>{{ user.name }}</h2>
        <p>{{ user.email }}</p>
      </ion-label>
      <ion-button size="small" color="danger" (click)="deleteUser(user.id)">
        Delete
      </ion-button>
    </ion-item>
  </ion-list>
</ion-content>

<!DOCTYPE html>
<html>
<head>
    <title>SQLite Test</title>
</head>
<body>
    <h1>SQLite CRUD Test</h1>
    <p>Open the browser console to see the test results.</p>
    
    <script>
        console.log('Testing SQLite functionality...');
        
        // Test if jeep-sqlite is available
        if (typeof customElements !== 'undefined') {
            console.log('✅ Custom elements supported');
            
            // Check if jeep-sqlite is defined
            customElements.whenDefined('jeep-sqlite').then(() => {
                console.log('✅ jeep-sqlite web component is defined');
                
                // Check if element exists in DOM
                const jeepSqlite = document.querySelector('jeep-sqlite');
                if (jeepSqlite) {
                    console.log('✅ jeep-sqlite element found in DOM');
                } else {
                    console.log('❌ jeep-sqlite element not found in DOM');
                }
            }).catch(err => {
                console.log('❌ jeep-sqlite not defined:', err);
            });
        } else {
            console.log('❌ Custom elements not supported');
        }
    </script>
</body>
</html>
